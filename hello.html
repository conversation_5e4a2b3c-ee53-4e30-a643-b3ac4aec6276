<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sum Calculator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .calculator {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        
        .input-group {
            margin: 20px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: bold;
        }
        
        input[type="number"] {
            width: 200px;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            text-align: center;
        }
        
        input[type="number"]:focus {
            border-color: #4CAF50;
            outline: none;
        }
        
        button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px 0;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #e8f5e8;
            border: 2px solid #4CAF50;
            border-radius: 5px;
            font-size: 18px;
            font-weight: bold;
            color: #2e7d2e;
            display: none;
        }
        
        .error {
            background-color: #ffe8e8;
            border-color: #ff4444;
            color: #cc0000;
        }
        
        .clear-btn {
            background-color: #ff6b6b;
            margin-left: 10px;
        }
        
        .clear-btn:hover {
            background-color: #ff5252;
        }
    </style>
</head>
<body>
    <div class="calculator">
        <h1>Sum Calculator</h1>
        
        <div class="input-group">
            <label for="number1">First Number:</label>
            <input type="number" id="number1" placeholder="Enter first number" step="any">
        </div>
        
        <div class="input-group">
            <label for="number2">Second Number:</label>
            <input type="number" id="number2" placeholder="Enter second number" step="any">
        </div>
        
        <button onclick="calculateSum()">Calculate Sum</button>
        <button class="clear-btn" onclick="clearInputs()">Clear</button>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        function calculateSum() {
            // Get input values
            const num1 = document.getElementById('number1').value;
            const num2 = document.getElementById('number2').value;
            const resultDiv = document.getElementById('result');
            
            // Check if both inputs have values
            if (num1 === '' || num2 === '') {
                showResult('Please enter both numbers!', true);
                return;
            }
            
            // Convert to numbers and calculate sum
            const number1 = parseFloat(num1);
            const number2 = parseFloat(num2);
            
            // Check if inputs are valid numbers
            if (isNaN(number1) || isNaN(number2)) {
                showResult('Please enter valid numbers!', true);
                return;
            }
            
            const sum = number1 + number2;
            
            // Display result
            showResult(`${number1} + ${number2} = ${sum}`, false);
        }
        
        function showResult(message, isError) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.style.display = 'block';
            
            // Apply error styling if needed
            if (isError) {
                resultDiv.classList.add('error');
            } else {
                resultDiv.classList.remove('error');
            }
        }
        
        function clearInputs() {
            document.getElementById('number1').value = '';
            document.getElementById('number2').value = '';
            document.getElementById('result').style.display = 'none';
            document.getElementById('number1').focus();
        }
        
        // Allow Enter key to calculate
        document.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                calculateSum();
            }
        });
        
        // Focus on first input when page loads
        window.onload = function() {
            document.getElementById('number1').focus();
        };
    </script>
</body>
</html>
